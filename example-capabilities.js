// Example WebDriverIO capabilities for mobile automation
// Copy and modify this file according to your testing needs

// Android capabilities
export const androidCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554', // Your device name or emulator
  'appium:app': '/path/to/your/app.apk', // Path to your APK file
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300,
  'appium:connectHardwareKeyboard': true
};

// iOS capabilities
export const iosCapabilities = {
  platformName: 'iOS',
  'appium:deviceName': 'iPhone 14', // Your device name or simulator
  'appium:app': '/path/to/your/app.ipa', // Path to your IPA file
  'appium:automationName': 'XCUITest',
  'appium:newCommandTimeout': 300,
  'appium:connectHardwareKeyboard': true
};

// Web capabilities for mobile browsers
export const mobileWebCapabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:browserName': 'Chrome',
  'appium:automationName': 'UiAutomator2',
  'appium:newCommandTimeout': 300
};

// Remote Appium server configuration
export const remoteConfig = {
  hostname: 'localhost',
  port: 4723,
  path: '/wd/hub'
};

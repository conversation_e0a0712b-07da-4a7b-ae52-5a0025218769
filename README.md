# AgentQ Mobile Automation Test

NPM library for low-code mobile automation testing using WebDriverIO and AgentQ AI. This library enables natural language-driven mobile app testing through AI-powered commands.

## Features

- 🤖 AI-powered test automation using natural language commands
- 📱 Mobile app testing for Android and iOS
- 🔧 WebDriverIO-based automation framework
- 🌐 WebSocket communication with AgentQ AI service
- 📊 Automatic test result reporting
- 🎯 Support for various mobile actions (tap, swipe, scroll, etc.)

## Installation

```bash
npm install agentq_mobile_automation_test
```

## Prerequisites

1. **Appium Server**: Install and start Appium
```bash
npm install -g appium
appium
```

2. **Mobile Drivers**: Install required drivers
```bash
# For Android
appium driver install uiautomator2

# For iOS
appium driver install xcuitest
```

3. **Environment Variables**: Set up your AgentQ credentials
```bash
export AGENTQ_TOKEN="your-agentq-token"
export AGENTQ_SERVICE_URL="wss://websocket-ai-automation-test-api.agentq.id"
```

## Quick Start

### Basic Usage

```typescript
import { q, initBrowser, closeBrowser } from 'agentq_mobile_automation_test';

// Android capabilities
const capabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2'
};

async function runTest() {
  // Initialize browser
  const browser = await initBrowser(capabilities);

  try {
    // Use natural language commands
    await q("tap on the login button");
    await q("enter '<EMAIL>' in the email field");
    await q("enter 'password123' in the password field");
    await q("tap on submit");
    await q("verify that welcome message is displayed");
  } finally {
    // Clean up
    await closeBrowser();
  }
}

runTest().catch(console.error);
```

### Using Test Framework

```typescript
import { q, runTest } from 'agentq_mobile_automation_test';

const capabilities = {
  platformName: 'Android',
  'appium:deviceName': 'emulator-5554',
  'appium:app': '/path/to/your/app.apk',
  'appium:automationName': 'UiAutomator2'
};

runTest('001-Login Test', capabilities, async ({ browser }) => {
  await q("open the app");
  await q("tap on login button");
  await q("enter username 'testuser'");
  await q("enter password 'testpass'");
  await q("tap submit button");
  await q("verify login success message appears");
}).catch(console.error);
```

## Configuration

### Capabilities Examples

See `example-capabilities.js` for detailed configuration examples:

- Android app testing
- iOS app testing
- Mobile web browser testing
- Remote Appium server setup

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AGENTQ_TOKEN` | Your AgentQ API token | Yes |
| `AGENTQ_SERVICE_URL` | WebSocket service URL | No (defaults provided) |
| `AGENTQ_PROJECT_ID` | Project ID for test reporting | No |
| `AGENTQ_TESTRUN_ID` | Test run ID for result tracking | No |

## Supported Commands

The AI understands natural language commands for mobile automation:

### Navigation
- "open the app"
- "go to settings page"
- "navigate back"

### Interactions
- "tap on login button"
- "click the submit button"
- "enter 'text' in the username field"
- "select option from dropdown"

### Gestures
- "swipe left/right/up/down"
- "scroll down to find element"
- "pinch to zoom"

### Assertions
- "verify that text is displayed"
- "check if button is enabled"
- "assert that page title contains 'Welcome'"

## API Reference

### Core Functions

#### `q(command: string): Promise<void>`
Execute a natural language command through AgentQ AI.

#### `initBrowser(capabilities: any): Promise<Browser>`
Initialize WebDriverIO browser with given capabilities.

#### `closeBrowser(): Promise<void>`
Close the browser session and clean up resources.

#### `runTest(title: string, capabilities: any, testFn: Function): Promise<void>`
Run a complete test with automatic setup, execution, and cleanup.

## Scripts

```bash
# Build the library
npm run build

# Pull test cases from AgentQ platform
npm run pull-testcase

# Start Appium server
npm run start-appium
```

## Troubleshooting

### Common Issues

1. **Appium Connection Failed**
   - Ensure Appium server is running on port 4723
   - Check device/emulator is connected and accessible

2. **App Not Found**
   - Verify the app path in capabilities is correct
   - Ensure the app is compatible with the target device

3. **WebSocket Connection Issues**
   - Check AGENTQ_TOKEN is valid
   - Verify network connectivity to AgentQ service

### Debug Mode

Enable debug logging:
```bash
export DEBUG=agentq:*
```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License - see LICENSE file for details.

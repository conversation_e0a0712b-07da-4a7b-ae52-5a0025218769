{"name": "agentq_mobile_automation_test", "version": "1.0.0", "description": "NPM library for low-code mobile automation testing using WebDriverIO and AgentQ AI", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepare": "npm run build", "test": "wdio run", "test:android": "wdio run --spec ./tests/**/*.spec.ts --platform android", "test:ios": "wdio run --spec ./tests/**/*.spec.ts --platform ios", "pull-testcase": "node dist/pull-testcase.js", "start-appium": "appium"}, "repository": {"type": "git", "url": "git+https://github.com/agentq-ai/agentq_mobile_automation_test.git"}, "keywords": ["agentq", "automation", "test"], "author": {"name": "agentq.id", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/agentq-ai/agentq_mobile_automation_test/issues"}, "homepage": "https://github.com/agentq-ai/agentq_mobile_automation_test#readme", "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^24.3.0", "@types/ws": "^8.18.1", "@wdio/appium-service": "^9.19.1", "@wdio/cli": "^9.19.1", "@wdio/globals": "^9.17.0", "@wdio/junit-reporter": "^9.19.1", "@wdio/local-runner": "^9.19.1", "@wdio/mocha-framework": "^9.19.1", "@wdio/spec-reporter": "^9.19.1", "appium": "^3.0.1", "typescript": "^5.9.2"}, "dependencies": {"i": "^0.3.7", "npm": "^11.5.2", "webdriverio": "^9.19.1"}}
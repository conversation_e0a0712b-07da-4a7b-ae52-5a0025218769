{"name": "agentq_mobile_automation_test", "version": "1.0.0", "description": "NPM library for low-code mobile automation test from agents app", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "prepare": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/agentq-ai/agentq_mobile_automation_test.git"}, "keywords": ["agentq", "automation", "test"], "author": {"name": "agentq.id", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/agentq-ai/agentq_mobile_automation_test/issues"}, "homepage": "https://github.com/agentq-ai/agentq_mobile_automation_test#readme", "devDependencies": {"@types/node": "^24.3.0", "@types/ws": "^8.18.1", "typescript": "^5.9.2"}, "dependencies": {"i": "^0.3.7", "npm": "^11.5.2", "webdriverio": "^9.19.1"}}
import { <PERSON><PERSON><PERSON> } from 'webdriverio';
import { AICommand } from './types';

export class CommandExecutor {
  static async execute(command: AICommand, browser: Browser): Promise<void> {
    try {
      if (command.action === 'fill') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.setValue(command.value as string);
      } else if (command.action === 'click') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.click();
      } else if (command.action === 'tap') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.touchAction('tap');
      } else if (command.action === 'visit' || command.action === 'goto' || command.action === 'navigate' || command.action === 'open') {
        await browser.url(command.value as string);
      } else if (command.action === 'select') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        if (typeof command.value === 'object' && command.value !== null && 'value' in command.value) {
          await element.selectByAttribute('value', command.value.value || '');
        } else if (Array.isArray(command.value)) {
          for (const value of command.value) {
            await element.selectByVisibleText(value);
          }
        } else if (typeof command.value === 'string') {
          await element.selectByVisibleText(command.value);
        } else if (command.value !== undefined) {
          throw new Error('command.value is not a string or an object with value, label, and index properties');
        } else {
          throw new Error('command.value is undefined');
        }
      } else if (command.action === 'check') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const isSelected = await element.isSelected();
        if (!isSelected) {
          await element.click();
        }
      } else if (command.action === 'uncheck') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const isSelected = await element.isSelected();
        if (isSelected) {
          await element.click();
        }
      } else if (command.action === 'type') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        await element.addValue(command.value as string);
      } else if (command.action === 'wait') {
        await browser.pause(command.value as number);
      } else if (command.action === 'swipe') {
        if (typeof command.value === 'object' && command.value !== null && 'direction' in command.value) {
          const swipeValue = command.value as { direction: string; distance?: number };
          const distance = swipeValue.distance || 500;

          const { width, height } = await browser.getWindowSize();
          const centerX = width / 2;
          const centerY = height / 2;

          let startX = centerX, startY = centerY, endX = centerX, endY = centerY;

          switch (swipeValue.direction.toLowerCase()) {
            case 'up':
              startY = centerY + distance / 2;
              endY = centerY - distance / 2;
              break;
            case 'down':
              startY = centerY - distance / 2;
              endY = centerY + distance / 2;
              break;
            case 'left':
              startX = centerX + distance / 2;
              endX = centerX - distance / 2;
              break;
            case 'right':
              startX = centerX - distance / 2;
              endX = centerX + distance / 2;
              break;
          }

          await browser.touchAction([
            { action: 'press', x: startX, y: startY },
            { action: 'wait', ms: 100 },
            { action: 'moveTo', x: endX, y: endY },
            { action: 'release' }
          ]);
        }
      } else if (command.action === 'scroll') {
        if (command.target) {
          const element = browser.$(command.target);
          await element.scrollIntoView();
        } else if (typeof command.value === 'object' && command.value !== null && 'direction' in command.value) {
          const scrollValue = command.value as { direction: string; distance?: number };
          const distance = scrollValue.distance || 300;

          switch (scrollValue.direction.toLowerCase()) {
            case 'up':
              await browser.execute('mobile: scroll', { direction: 'up', distance });
              break;
            case 'down':
              await browser.execute('mobile: scroll', { direction: 'down', distance });
              break;
          }
        }
      } else if (command.action === 'assertVisible') {
        const element = browser.$(command.target);
        await element.waitForDisplayed({ timeout: 10000 });
      } else if (command.action === 'assertNotVisible') {
        const element = browser.$(command.target);
        await element.waitForDisplayed({ timeout: 10000, reverse: true });
      } else if (command.action === 'assertEqual') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const textContent = await element.getText();
        if (textContent !== command.value) {
          throw new Error(`Expected "${command.value}" but got "${textContent}"`);
        }
      } else if (command.action === 'assertContain') {
        const element = browser.$(command.target);
        await element.waitForExist({ timeout: 10000 });
        const textContent = await element.getText();
        if (!textContent.includes(command.value as string)) {
          throw new Error(`Expected text to contain "${command.value}" but got "${textContent}"`);
        }
      }
      // Add more command handling here as needed
    } catch (error) {
      console.error(`Error executing command ${command.action} on ${command.target}:`, error);
      throw error;
    }
  }
}
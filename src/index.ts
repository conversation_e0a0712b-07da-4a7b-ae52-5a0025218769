import { Browser, remote } from 'webdriverio';
import { WSClient } from './wsClient';
import { BrowserContext } from './pageContext';
import { config } from 'dotenv';
import { exportTestResult } from './testResult';
import { TestResult } from './types';
import { getAccessToken } from './testResult';

config();

export async function q(userPrompt: string): Promise<void> {
  const browser = BrowserContext.getInstance().getBrowser();
  return WSClient.getInstance().sendCommand(userPrompt, browser);
}

// WebDriverIO browser initialization function
export async function initBrowser(capabilities: any): Promise<Browser> {
  const browser = await remote(capabilities);
  BrowserContext.getInstance().setBrowser(browser);
  return browser;
}

// Helper function to close browser
export async function closeBrowser(): Promise<void> {
  await BrowserContext.getInstance().closeBrowser();
}

// WebDriverIO test framework integration
let cachedAccessToken: string | null = null;

export interface TestContext {
  browser: Browser;
  testTitle: string;
}

export async function runTest(
  testTitle: string,
  capabilities: any,
  testFunction: (context: TestContext) => Promise<void>
): Promise<void> {
  const startTime = Date.now();
  let browser: Browser | null = null;

  try {
    // Initialize browser
    browser = await initBrowser(capabilities);

    // Get access token once before the test starts
    if (!cachedAccessToken) {
      cachedAccessToken = await getAccessToken();
    }

    // Run the test
    await testFunction({ browser, testTitle });

    // Extract tcId from test title (format: "12345-Test Title")
    const tcId = parseInt(testTitle.split('-')[0]);
    if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
      const executionTime = (Date.now() - startTime) / 1000;
      await exportTestResult(
        tcId.toString(),
        tcId.toString(),
        process.env.AGENTQ_TESTRUN_ID,
        {
          status: 'passed',
          actualResult: `Test "${testTitle}" passed successfully`,
          executionTime,
          notes: 'Test completed without errors'
        }
      );
    }
  } catch (error: any) {
    // Extract tcId from test title (format: "12345-Test Title")
    const tcId = parseInt(testTitle.split('-')[0]);
    if (!isNaN(tcId) && process.env.AGENTQ_TESTRUN_ID && cachedAccessToken) {
      const executionTime = (Date.now() - startTime) / 1000;
      await exportTestResult(
        tcId.toString(),
        tcId.toString(),
        process.env.AGENTQ_TESTRUN_ID,
        {
          status: 'failed',
          actualResult: error.message,
          executionTime,
          notes: `Test failed: ${error.message}`
        }
      );
    }
    throw error;
  } finally {
    // Clean up browser
    if (browser) {
      await closeBrowser();
    }
  }
}

// Simple assertion function for WebDriverIO
export function expect(actual: any) {
  return {
    toBe: (expected: any) => {
      if (actual !== expected) {
        throw new Error(`Expected "${expected}" but got "${actual}"`);
      }
    },
    toContain: (expected: any) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected "${actual}" to contain "${expected}"`);
      }
    },
    toBeDisplayed: async () => {
      if (actual && typeof actual.isDisplayed === 'function') {
        const isDisplayed = await actual.isDisplayed();
        if (!isDisplayed) {
          throw new Error('Element is not displayed');
        }
      } else {
        throw new Error('Invalid element for toBeDisplayed assertion');
      }
    }
  };
}